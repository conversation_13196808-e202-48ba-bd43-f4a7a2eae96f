import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { ilike, eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { query, searchType } = await request.json();

    if (!query || !searchType) {
      return NextResponse.json(
        { error: 'Query and search type are required' },
        { status: 400 }
      );
    }

    let searchCondition;

    switch (searchType) {
      case 'name':
        searchCondition = ilike(candidates.fullName, `%${query}%`);
        break;
      case 'email':
        searchCondition = eq(candidates.email, query);
        break;
      case 'passport':
        searchCondition = eq(candidates.passportNumber, query);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid search type' },
          { status: 400 }
        );
    }

    // Get candidates with their test results (if any)
    const candidatesWithResults = await db
      .select({
        id: candidates.id,
        fullName: candidates.fullName,
        email: candidates.email,
        phoneNumber: candidates.phoneNumber,
        dateOfBirth: candidates.dateOfBirth,
        nationality: candidates.nationality,
        passportNumber: candidates.passportNumber,
        testDate: candidates.testDate,
        testCenter: candidates.testCenter,
        photoUrl: candidates.photoUrl,
        resultId: testResults.id,
        hasResults: testResults.id,
      })
      .from(candidates)
      .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
      .where(searchCondition)
      .orderBy(candidates.fullName);

    // Transform the results to group by candidate
    const candidateMap = new Map();

    candidatesWithResults.forEach((row) => {
      if (!candidateMap.has(row.id)) {
        candidateMap.set(row.id, {
          id: row.id,
          fullName: row.fullName,
          email: row.email,
          phoneNumber: row.phoneNumber,
          dateOfBirth: row.dateOfBirth,
          nationality: row.nationality,
          passportNumber: row.passportNumber,
          testDate: row.testDate,
          testCenter: row.testCenter,
          photoUrl: row.photoUrl,
          hasResults: !!row.hasResults,
          resultId: row.resultId,
        });
      }
    });

    const results = Array.from(candidateMap.values());

    return NextResponse.json(results);
  } catch (error) {
    console.error('Candidate search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
