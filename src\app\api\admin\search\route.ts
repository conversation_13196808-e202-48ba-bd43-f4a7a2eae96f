import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { ilike, eq, or, and, gte, lte } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const filters = await request.json();

    // Build search conditions
    const conditions = [];

    // Text search
    if (filters.query) {
      const query = filters.query.trim();
      if (query) {
        switch (filters.searchType) {
          case 'name':
            conditions.push(ilike(candidates.fullName, `%${query}%`));
            break;
          case 'email':
            conditions.push(ilike(candidates.email, `%${query}%`));
            break;
          case 'passport':
            conditions.push(ilike(candidates.passportNumber, `%${query}%`));
            break;
          default: // 'all'
            conditions.push(
              or(
                ilike(candidates.fullName, `%${query}%`),
                ilike(candidates.email, `%${query}%`),
                ilike(candidates.passportNumber, `%${query}%`)
              )
            );
        }
      }
    }

    // Test center filter
    if (filters.testCenter) {
      conditions.push(eq(candidates.testCenter, filters.testCenter));
    }

    // Test date range
    if (filters.testDateFrom) {
      conditions.push(gte(candidates.testDate, new Date(filters.testDateFrom)));
    }
    if (filters.testDateTo) {
      conditions.push(lte(candidates.testDate, new Date(filters.testDateTo)));
    }

    // Nationality filter
    if (filters.nationality) {
      conditions.push(ilike(candidates.nationality, `%${filters.nationality}%`));
    }

    // Combine all conditions
    const whereCondition = conditions.length > 0 ? and(...conditions) : undefined;

    // Get candidates with their test results
    const candidatesWithResults = await db
      .select({
        candidate: candidates,
        result: testResults,
      })
      .from(candidates)
      .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
      .where(whereCondition)
      .orderBy(candidates.fullName);

    // Process results and apply additional filters
    const candidateMap = new Map();

    candidatesWithResults.forEach((row) => {
      const candidateId = row.candidate.id;

      if (!candidateMap.has(candidateId)) {
        candidateMap.set(candidateId, {
          id: row.candidate.id,
          fullName: row.candidate.fullName,
          email: row.candidate.email,
          phoneNumber: row.candidate.phoneNumber,
          passportNumber: row.candidate.passportNumber,
          nationality: row.candidate.nationality,
          testDate: row.candidate.testDate,
          testCenter: row.candidate.testCenter,
          photoUrl: row.candidate.photoUrl,
          hasResults: !!row.result,
          result: row.result ? {
            id: row.result.id,
            overallBandScore: row.result.overallBandScore,
            status: row.result.status,
            createdAt: row.result.createdAt,
          } : null,
        });
      }
    });

    let results = Array.from(candidateMap.values());

    // Apply post-processing filters
    if (filters.hasResults !== 'all') {
      if (filters.hasResults === 'yes') {
        results = results.filter(r => r.hasResults);
      } else if (filters.hasResults === 'no') {
        results = results.filter(r => !r.hasResults);
      }
    }

    if (filters.resultStatus !== 'all' && filters.resultStatus) {
      results = results.filter(r => r.result?.status === filters.resultStatus);
    }

    if (filters.bandScoreMin) {
      const minScore = parseFloat(filters.bandScoreMin);
      results = results.filter(r =>
        r.result?.overallBandScore && r.result.overallBandScore >= minScore
      );
    }

    if (filters.bandScoreMax) {
      const maxScore = parseFloat(filters.bandScoreMax);
      results = results.filter(r =>
        r.result?.overallBandScore && r.result.overallBandScore <= maxScore
      );
    }

    return NextResponse.json({
      results,
      total: results.length,
      filters,
    });

  } catch (error) {
    console.error('Advanced search error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
