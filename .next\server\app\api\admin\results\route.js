/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/results/route";
exports.ids = ["app/api/admin/results/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fresults%2Froute&page=%2Fapi%2Fadmin%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fresults%2Froute&page=%2Fapi%2Fadmin%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/results/route.ts */ \"(rsc)/./src/app/api/admin/results/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_results_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_results_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/results/route\",\n        pathname: \"/api/admin/results\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/results/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\results\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_results_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fresults%2Froute&page=%2Fapi%2Fadmin%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/results/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/admin/results/route.ts ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Forbidden - Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const status = searchParams.get('status');\n        const search = searchParams.get('search');\n        const offset = (page - 1) * limit;\n        try {\n            // Build where conditions\n            const whereConditions = [];\n            if (status && status !== 'all') {\n                whereConditions.push((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status, status));\n            }\n            if (search) {\n                const searchCondition = (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.like)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.like)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.like)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, `%${search}%`));\n                if (searchCondition) {\n                    whereConditions.push(searchCondition);\n                }\n            }\n            const whereClause = whereConditions.length > 0 ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.and)(...whereConditions) : undefined;\n            // Get total count\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).where(whereClause);\n            const total = totalResult[0]?.count || 0;\n            // Get results with pagination\n            const results = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n                candidateId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId,\n                listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n                readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n                writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n                speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n                overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n                status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status,\n                certificateGenerated: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.certificateGenerated,\n                createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt,\n                updatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.updatedAt,\n                enteredBy: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy,\n                verifiedBy: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.verifiedBy,\n                candidate: {\n                    fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                    email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n                    passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                    nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n                    testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testDate,\n                    testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter\n                },\n                checker: {\n                    name: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.name,\n                    email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email\n                }\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id)).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.users, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.enteredBy, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.id)).where(whereClause).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.createdAt)).limit(limit).offset(offset);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                results,\n                total,\n                page,\n                totalPages: Math.ceil(total / limit)\n            });\n        } catch (dbError) {\n            console.warn('Database connection failed, returning empty results:', dbError);\n            // Return empty results when database is not available\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                results: [],\n                total: 0,\n                page: 1,\n                totalPages: 0\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching admin results:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        if (session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Forbidden - Admin access required'\n            }, {\n                status: 403\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        if (!data.candidateId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate ID is required'\n            }, {\n                status: 400\n            });\n        }\n        // Check if candidate exists\n        const candidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, data.candidateId)).limit(1);\n        if (!candidate.length) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Candidate not found'\n            }, {\n                status: 404\n            });\n        }\n        // Prepare data for database insertion (keep as strings for decimal fields)\n        const processedData = {\n            candidateId: data.candidateId,\n            // Listening scores\n            listeningScore: data.listeningScore || null,\n            listeningBandScore: data.listeningBandScore || null,\n            // Reading scores\n            readingScore: data.readingScore || null,\n            readingBandScore: data.readingBandScore || null,\n            // Writing scores\n            writingTask1Score: data.writingTask1Score || null,\n            writingTask2Score: data.writingTask2Score || null,\n            writingBandScore: data.writingBandScore || null,\n            // Speaking scores\n            speakingFluencyScore: data.speakingFluencyScore || null,\n            speakingLexicalScore: data.speakingLexicalScore || null,\n            speakingGrammarScore: data.speakingGrammarScore || null,\n            speakingPronunciationScore: data.speakingPronunciationScore || null,\n            speakingBandScore: data.speakingBandScore || null,\n            // Overall score\n            overallBandScore: data.overallBandScore || null,\n            // Metadata\n            status: data.status || 'pending',\n            enteredBy: session.user?.id\n        };\n        // Insert the test result\n        const result = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults).values(processedData).returning();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result[0], {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating test result:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/results/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL3NjaGVtYS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0c7QUFDaEQ7QUFFaEQsaUNBQWlDO0FBQzFCLE1BQU1RLFFBQVFSLDREQUFPQSxDQUFDLFNBQVM7SUFDcENTLElBQUlSLHlEQUFJQSxDQUFDLE1BQU1TLFVBQVUsR0FBR0MsVUFBVSxDQUFDLElBQU1KLDhEQUFRQTtJQUNyREssTUFBTVgseURBQUlBLENBQUM7SUFDWFksT0FBT1oseURBQUlBLENBQUMsU0FBU2EsT0FBTyxHQUFHQyxNQUFNO0lBQ3JDQyxlQUFlZCw4REFBU0EsQ0FBQyxpQkFBaUI7UUFBRWUsTUFBTTtJQUFPO0lBQ3pEQyxPQUFPakIseURBQUlBLENBQUM7SUFDWmtCLFVBQVVsQix5REFBSUEsQ0FBQztJQUNmbUIsTUFBTW5CLHlEQUFJQSxDQUFDLFFBQVE7UUFBRW9CLE1BQU07WUFBQztZQUFTO1NBQWU7SUFBQyxHQUFHUCxPQUFPLEdBQUdRLE9BQU8sQ0FBQztJQUMxRUMsV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCwyQkFBMkI7QUFDcEIsTUFBTVksV0FBVzFCLDREQUFPQSxDQUFDLFlBQVk7SUFDMUMyQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGQyxNQUFNN0IseURBQUlBLENBQUMsUUFBUWEsT0FBTztJQUMxQmlCLFVBQVU5Qix5REFBSUEsQ0FBQyxZQUFZYSxPQUFPO0lBQ2xDa0IsbUJBQW1CL0IseURBQUlBLENBQUMscUJBQXFCYSxPQUFPO0lBQ3BEbUIsZUFBZWhDLHlEQUFJQSxDQUFDO0lBQ3BCaUMsY0FBY2pDLHlEQUFJQSxDQUFDO0lBQ25Ca0MsWUFBWWhDLDREQUFPQSxDQUFDO0lBQ3BCaUMsWUFBWW5DLHlEQUFJQSxDQUFDO0lBQ2pCb0MsT0FBT3BDLHlEQUFJQSxDQUFDO0lBQ1pxQyxVQUFVckMseURBQUlBLENBQUM7SUFDZnNDLGVBQWV0Qyx5REFBSUEsQ0FBQztBQUN0QixHQUFHO0FBRUgsb0NBQW9DO0FBQzdCLE1BQU11QyxXQUFXeEMsNERBQU9BLENBQUMsWUFBWTtJQUMxQ3lDLGNBQWN4Qyx5REFBSUEsQ0FBQyxnQkFBZ0JTLFVBQVU7SUFDN0NpQixRQUFRMUIseURBQUlBLENBQUMsVUFBVWEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTXBCLE1BQU1DLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBQ2xGYSxTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsc0JBQXNCO0FBQ2YsTUFBTTZCLHFCQUFxQjNDLDREQUFPQSxDQUFDLHNCQUFzQjtJQUM5RDRDLFlBQVkzQyx5REFBSUEsQ0FBQyxjQUFjYSxPQUFPO0lBQ3RDK0IsT0FBTzVDLHlEQUFJQSxDQUFDLFNBQVNhLE9BQU87SUFDNUI0QixTQUFTeEMsOERBQVNBLENBQUMsV0FBVztRQUFFZSxNQUFNO0lBQU8sR0FBR0gsT0FBTztBQUN6RCxHQUFHO0FBRUgsbUJBQW1CO0FBQ1osTUFBTWdDLGFBQWE5Qyw0REFBT0EsQ0FBQyxjQUFjO0lBQzlDUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckR3QyxVQUFVOUMseURBQUlBLENBQUMsYUFBYWEsT0FBTztJQUNuQ0QsT0FBT1oseURBQUlBLENBQUMsU0FBU2EsT0FBTyxHQUFHQyxNQUFNO0lBQ3JDaUMsYUFBYS9DLHlEQUFJQSxDQUFDLGdCQUFnQmEsT0FBTztJQUN6Q21DLGFBQWEvQyw4REFBU0EsQ0FBQyxpQkFBaUI7UUFBRWUsTUFBTTtJQUFPLEdBQUdILE9BQU87SUFDakVvQyxhQUFhakQseURBQUlBLENBQUMsZUFBZWEsT0FBTztJQUN4Q3FDLGdCQUFnQmxELHlEQUFJQSxDQUFDLG1CQUFtQmEsT0FBTyxHQUFHQyxNQUFNO0lBQ3hEcUMsVUFBVWxELDhEQUFTQSxDQUFDLGFBQWE7UUFBRWUsTUFBTTtJQUFPLEdBQUdILE9BQU87SUFDMUR1QyxZQUFZcEQseURBQUlBLENBQUMsZUFBZWEsT0FBTztJQUN2Q3dDLFVBQVVyRCx5REFBSUEsQ0FBQztJQUNmc0IsV0FBV3JCLDhEQUFTQSxDQUFDLGNBQWNzQixVQUFVLEdBQUdWLE9BQU87SUFDdkRXLFdBQVd2Qiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0FBQ3pELEdBQUc7QUFFSCxxQkFBcUI7QUFDZCxNQUFNeUMsY0FBY3ZELDREQUFPQSxDQUFDLGdCQUFnQjtJQUNqRFMsSUFBSVIseURBQUlBLENBQUMsTUFBTVMsVUFBVSxHQUFHQyxVQUFVLENBQUMsSUFBTUosOERBQVFBO0lBQ3JEaUQsYUFBYXZELHlEQUFJQSxDQUFDLGdCQUFnQmEsT0FBTyxHQUFHYyxVQUFVLENBQUMsSUFBTWtCLFdBQVdyQyxFQUFFLEVBQUU7UUFBRW9CLFVBQVU7SUFBVTtJQUVsRyxtQkFBbUI7SUFDbkI0QixnQkFBZ0JyRCw0REFBT0EsQ0FBQyxtQkFBbUI7UUFBRXNELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQ3BFQyxvQkFBb0J4RCw0REFBT0EsQ0FBQyx3QkFBd0I7UUFBRXNELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBRTdFLGlCQUFpQjtJQUNqQkUsY0FBY3pELDREQUFPQSxDQUFDLGlCQUFpQjtRQUFFc0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFDaEVHLGtCQUFrQjFELDREQUFPQSxDQUFDLHNCQUFzQjtRQUFFc0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFekUsaUJBQWlCO0lBQ2pCSSxtQkFBbUIzRCw0REFBT0EsQ0FBQyx1QkFBdUI7UUFBRXNELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQzNFSyxtQkFBbUI1RCw0REFBT0EsQ0FBQyx1QkFBdUI7UUFBRXNELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBQzNFTSxrQkFBa0I3RCw0REFBT0EsQ0FBQyxzQkFBc0I7UUFBRXNELFdBQVc7UUFBR0MsT0FBTztJQUFFO0lBRXpFLGtCQUFrQjtJQUNsQk8sc0JBQXNCOUQsNERBQU9BLENBQUMsMEJBQTBCO1FBQUVzRCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUNqRlEsc0JBQXNCL0QsNERBQU9BLENBQUMsMEJBQTBCO1FBQUVzRCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUNqRlMsc0JBQXNCaEUsNERBQU9BLENBQUMsMEJBQTBCO1FBQUVzRCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUNqRlUsNEJBQTRCakUsNERBQU9BLENBQUMsZ0NBQWdDO1FBQUVzRCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUM3RlcsbUJBQW1CbEUsNERBQU9BLENBQUMsdUJBQXVCO1FBQUVzRCxXQUFXO1FBQUdDLE9BQU87SUFBRTtJQUUzRSxnQkFBZ0I7SUFDaEJZLGtCQUFrQm5FLDREQUFPQSxDQUFDLHNCQUFzQjtRQUFFc0QsV0FBVztRQUFHQyxPQUFPO0lBQUU7SUFFekUsc0JBQXNCO0lBQ3RCYSxRQUFRdkUseURBQUlBLENBQUMsVUFBVTtRQUFFb0IsTUFBTTtZQUFDO1lBQVc7WUFBYTtTQUFXO0lBQUMsR0FBR1AsT0FBTyxHQUFHUSxPQUFPLENBQUM7SUFDekZtRCxXQUFXeEUseURBQUlBLENBQUMsY0FBYzJCLFVBQVUsQ0FBQyxJQUFNcEIsTUFBTUMsRUFBRTtJQUN2RGlFLFlBQVl6RSx5REFBSUEsQ0FBQyxlQUFlMkIsVUFBVSxDQUFDLElBQU1wQixNQUFNQyxFQUFFO0lBQ3pEa0Usc0JBQXNCdEUsNERBQU9BLENBQUMseUJBQXlCaUIsT0FBTyxDQUFDO0lBQy9Ec0QsbUJBQW1CM0UseURBQUlBLENBQUMsc0JBQXNCYyxNQUFNO0lBQ3BEOEQsZ0JBQWdCNUUseURBQUlBLENBQUM7SUFDckI2RSxxQkFBcUJ6RSw0REFBT0EsQ0FBQyx5QkFBeUJpQixPQUFPLENBQUM7SUFDOUQ4QixVQUFVbEQsOERBQVNBLENBQUMsYUFBYTtRQUFFZSxNQUFNO0lBQU87SUFFaERNLFdBQVdyQiw4REFBU0EsQ0FBQyxjQUFjc0IsVUFBVSxHQUFHVixPQUFPO0lBQ3ZEVyxXQUFXdkIsOERBQVNBLENBQUMsY0FBY3NCLFVBQVUsR0FBR1YsT0FBTztBQUN6RCxHQUFHO0FBRUgsb0JBQW9CO0FBQ2IsTUFBTWlFLGFBQWEvRSw0REFBT0EsQ0FBQyxlQUFlO0lBQy9DUyxJQUFJUix5REFBSUEsQ0FBQyxNQUFNUyxVQUFVLEdBQUdDLFVBQVUsQ0FBQyxJQUFNSiw4REFBUUE7SUFDckR5RSxjQUFjL0UseURBQUlBLENBQUMsa0JBQWtCYSxPQUFPLEdBQUdjLFVBQVUsQ0FBQyxJQUFNMkIsWUFBWTlDLEVBQUUsRUFBRTtRQUFFb0IsVUFBVTtJQUFVO0lBRXRHLDBCQUEwQjtJQUMxQm9ELG1CQUFtQmhGLHlEQUFJQSxDQUFDO0lBQ3hCaUYsaUJBQWlCakYseURBQUlBLENBQUM7SUFDdEJrRixpQkFBaUJsRix5REFBSUEsQ0FBQztJQUN0Qm1GLGtCQUFrQm5GLHlEQUFJQSxDQUFDO0lBRXZCLHVDQUF1QztJQUN2Q29GLGlCQUFpQnBGLHlEQUFJQSxDQUFDO0lBQ3RCcUYsc0JBQXNCckYseURBQUlBLENBQUM7SUFFM0IsMkJBQTJCO0lBQzNCc0YsV0FBV2pGLHlEQUFJQSxDQUFDLGFBQWFrRixLQUFLO0lBQ2xDQyxZQUFZbkYseURBQUlBLENBQUMsY0FBY2tGLEtBQUs7SUFFcEMseUJBQXlCO0lBQ3pCRSxXQUFXcEYseURBQUlBLENBQUMsY0FBY2tGLEtBQUs7SUFPbkNHLGFBQWF6Riw4REFBU0EsQ0FBQyxnQkFBZ0JzQixVQUFVLEdBQUdWLE9BQU87QUFDN0QsR0FBRyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxXaW5kb3dzIDExXFxEZXNrdG9wXFxjb2Rlc1xcSUVMVFMtQ2VydGlmaWNhdGlvbi1TeXN0ZW1cXHNyY1xcbGliXFxkYlxcc2NoZW1hLnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBnVGFibGUsIHRleHQsIHRpbWVzdGFtcCwgaW50ZWdlciwgZGVjaW1hbCwgYm9vbGVhbiwganNvbiB9IGZyb20gJ2RyaXp6bGUtb3JtL3BnLWNvcmUnO1xuaW1wb3J0IHsgY3JlYXRlSWQgfSBmcm9tICdAcGFyYWxsZWxkcml2ZS9jdWlkMic7XG5cbi8vIFVzZXJzIHRhYmxlIGZvciBhdXRoZW50aWNhdGlvblxuZXhwb3J0IGNvbnN0IHVzZXJzID0gcGdUYWJsZSgndXNlcnMnLCB7XG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxuICBuYW1lOiB0ZXh0KCduYW1lJyksXG4gIGVtYWlsOiB0ZXh0KCdlbWFpbCcpLm5vdE51bGwoKS51bmlxdWUoKSxcbiAgZW1haWxWZXJpZmllZDogdGltZXN0YW1wKCdlbWFpbFZlcmlmaWVkJywgeyBtb2RlOiAnZGF0ZScgfSksXG4gIGltYWdlOiB0ZXh0KCdpbWFnZScpLFxuICBwYXNzd29yZDogdGV4dCgncGFzc3dvcmQnKSwgLy8gQWRkZWQgcGFzc3dvcmQgZmllbGQgZm9yIGF1dGhlbnRpY2F0aW9uXG4gIHJvbGU6IHRleHQoJ3JvbGUnLCB7IGVudW06IFsnYWRtaW4nLCAndGVzdF9jaGVja2VyJ10gfSkubm90TnVsbCgpLmRlZmF1bHQoJ3Rlc3RfY2hlY2tlcicpLFxuICBjcmVhdGVkQXQ6IHRpbWVzdGFtcCgnY3JlYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG4gIHVwZGF0ZWRBdDogdGltZXN0YW1wKCd1cGRhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbn0pO1xuXG4vLyBBY2NvdW50cyB0YWJsZSBmb3IgT0F1dGhcbmV4cG9ydCBjb25zdCBhY2NvdW50cyA9IHBnVGFibGUoJ2FjY291bnRzJywge1xuICB1c2VySWQ6IHRleHQoJ3VzZXJJZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkLCB7IG9uRGVsZXRlOiAnY2FzY2FkZScgfSksXG4gIHR5cGU6IHRleHQoJ3R5cGUnKS5ub3ROdWxsKCksXG4gIHByb3ZpZGVyOiB0ZXh0KCdwcm92aWRlcicpLm5vdE51bGwoKSxcbiAgcHJvdmlkZXJBY2NvdW50SWQ6IHRleHQoJ3Byb3ZpZGVyQWNjb3VudElkJykubm90TnVsbCgpLFxuICByZWZyZXNoX3Rva2VuOiB0ZXh0KCdyZWZyZXNoX3Rva2VuJyksXG4gIGFjY2Vzc190b2tlbjogdGV4dCgnYWNjZXNzX3Rva2VuJyksXG4gIGV4cGlyZXNfYXQ6IGludGVnZXIoJ2V4cGlyZXNfYXQnKSxcbiAgdG9rZW5fdHlwZTogdGV4dCgndG9rZW5fdHlwZScpLFxuICBzY29wZTogdGV4dCgnc2NvcGUnKSxcbiAgaWRfdG9rZW46IHRleHQoJ2lkX3Rva2VuJyksXG4gIHNlc3Npb25fc3RhdGU6IHRleHQoJ3Nlc3Npb25fc3RhdGUnKSxcbn0pO1xuXG4vLyBTZXNzaW9ucyB0YWJsZSBmb3IgYXV0aGVudGljYXRpb25cbmV4cG9ydCBjb25zdCBzZXNzaW9ucyA9IHBnVGFibGUoJ3Nlc3Npb25zJywge1xuICBzZXNzaW9uVG9rZW46IHRleHQoJ3Nlc3Npb25Ub2tlbicpLnByaW1hcnlLZXkoKSxcbiAgdXNlcklkOiB0ZXh0KCd1c2VySWQnKS5ub3ROdWxsKCkucmVmZXJlbmNlcygoKSA9PiB1c2Vycy5pZCwgeyBvbkRlbGV0ZTogJ2Nhc2NhZGUnIH0pLFxuICBleHBpcmVzOiB0aW1lc3RhbXAoJ2V4cGlyZXMnLCB7IG1vZGU6ICdkYXRlJyB9KS5ub3ROdWxsKCksXG59KTtcblxuLy8gVmVyaWZpY2F0aW9uIHRva2Vuc1xuZXhwb3J0IGNvbnN0IHZlcmlmaWNhdGlvblRva2VucyA9IHBnVGFibGUoJ3ZlcmlmaWNhdGlvblRva2VucycsIHtcbiAgaWRlbnRpZmllcjogdGV4dCgnaWRlbnRpZmllcicpLm5vdE51bGwoKSxcbiAgdG9rZW46IHRleHQoJ3Rva2VuJykubm90TnVsbCgpLFxuICBleHBpcmVzOiB0aW1lc3RhbXAoJ2V4cGlyZXMnLCB7IG1vZGU6ICdkYXRlJyB9KS5ub3ROdWxsKCksXG59KTtcblxuLy8gQ2FuZGlkYXRlcyB0YWJsZVxuZXhwb3J0IGNvbnN0IGNhbmRpZGF0ZXMgPSBwZ1RhYmxlKCdjYW5kaWRhdGVzJywge1xuICBpZDogdGV4dCgnaWQnKS5wcmltYXJ5S2V5KCkuJGRlZmF1bHRGbigoKSA9PiBjcmVhdGVJZCgpKSxcbiAgZnVsbE5hbWU6IHRleHQoJ2Z1bGxfbmFtZScpLm5vdE51bGwoKSxcbiAgZW1haWw6IHRleHQoJ2VtYWlsJykubm90TnVsbCgpLnVuaXF1ZSgpLFxuICBwaG9uZU51bWJlcjogdGV4dCgncGhvbmVfbnVtYmVyJykubm90TnVsbCgpLFxuICBkYXRlT2ZCaXJ0aDogdGltZXN0YW1wKCdkYXRlX29mX2JpcnRoJywgeyBtb2RlOiAnZGF0ZScgfSkubm90TnVsbCgpLFxuICBuYXRpb25hbGl0eTogdGV4dCgnbmF0aW9uYWxpdHknKS5ub3ROdWxsKCksXG4gIHBhc3Nwb3J0TnVtYmVyOiB0ZXh0KCdwYXNzcG9ydF9udW1iZXInKS5ub3ROdWxsKCkudW5pcXVlKCksXG4gIHRlc3REYXRlOiB0aW1lc3RhbXAoJ3Rlc3RfZGF0ZScsIHsgbW9kZTogJ2RhdGUnIH0pLm5vdE51bGwoKSxcbiAgdGVzdENlbnRlcjogdGV4dCgndGVzdF9jZW50ZXInKS5ub3ROdWxsKCksXG4gIHBob3RvVXJsOiB0ZXh0KCdwaG90b191cmwnKSxcbiAgY3JlYXRlZEF0OiB0aW1lc3RhbXAoJ2NyZWF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxuICB1cGRhdGVkQXQ6IHRpbWVzdGFtcCgndXBkYXRlZF9hdCcpLmRlZmF1bHROb3coKS5ub3ROdWxsKCksXG59KTtcblxuLy8gVGVzdCByZXN1bHRzIHRhYmxlXG5leHBvcnQgY29uc3QgdGVzdFJlc3VsdHMgPSBwZ1RhYmxlKCd0ZXN0X3Jlc3VsdHMnLCB7XG4gIGlkOiB0ZXh0KCdpZCcpLnByaW1hcnlLZXkoKS4kZGVmYXVsdEZuKCgpID0+IGNyZWF0ZUlkKCkpLFxuICBjYW5kaWRhdGVJZDogdGV4dCgnY2FuZGlkYXRlX2lkJykubm90TnVsbCgpLnJlZmVyZW5jZXMoKCkgPT4gY2FuZGlkYXRlcy5pZCwgeyBvbkRlbGV0ZTogJ2Nhc2NhZGUnIH0pLFxuXG4gIC8vIExpc3RlbmluZyBzY29yZXNcbiAgbGlzdGVuaW5nU2NvcmU6IGRlY2ltYWwoJ2xpc3RlbmluZ19zY29yZScsIHsgcHJlY2lzaW9uOiAzLCBzY2FsZTogMSB9KSxcbiAgbGlzdGVuaW5nQmFuZFNjb3JlOiBkZWNpbWFsKCdsaXN0ZW5pbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcblxuICAvLyBSZWFkaW5nIHNjb3Jlc1xuICByZWFkaW5nU2NvcmU6IGRlY2ltYWwoJ3JlYWRpbmdfc2NvcmUnLCB7IHByZWNpc2lvbjogMywgc2NhbGU6IDEgfSksXG4gIHJlYWRpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3JlYWRpbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcblxuICAvLyBXcml0aW5nIHNjb3Jlc1xuICB3cml0aW5nVGFzazFTY29yZTogZGVjaW1hbCgnd3JpdGluZ190YXNrMV9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcbiAgd3JpdGluZ1Rhc2syU2NvcmU6IGRlY2ltYWwoJ3dyaXRpbmdfdGFzazJfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXG4gIHdyaXRpbmdCYW5kU2NvcmU6IGRlY2ltYWwoJ3dyaXRpbmdfYmFuZF9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcblxuICAvLyBTcGVha2luZyBzY29yZXNcbiAgc3BlYWtpbmdGbHVlbmN5U2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX2ZsdWVuY3lfc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXG4gIHNwZWFraW5nTGV4aWNhbFNjb3JlOiBkZWNpbWFsKCdzcGVha2luZ19sZXhpY2FsX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxuICBzcGVha2luZ0dyYW1tYXJTY29yZTogZGVjaW1hbCgnc3BlYWtpbmdfZ3JhbW1hcl9zY29yZScsIHsgcHJlY2lzaW9uOiAyLCBzY2FsZTogMSB9KSxcbiAgc3BlYWtpbmdQcm9udW5jaWF0aW9uU2NvcmU6IGRlY2ltYWwoJ3NwZWFraW5nX3Byb251bmNpYXRpb25fc2NvcmUnLCB7IHByZWNpc2lvbjogMiwgc2NhbGU6IDEgfSksXG4gIHNwZWFraW5nQmFuZFNjb3JlOiBkZWNpbWFsKCdzcGVha2luZ19iYW5kX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxuXG4gIC8vIE92ZXJhbGwgc2NvcmVcbiAgb3ZlcmFsbEJhbmRTY29yZTogZGVjaW1hbCgnb3ZlcmFsbF9iYW5kX3Njb3JlJywgeyBwcmVjaXNpb246IDIsIHNjYWxlOiAxIH0pLFxuXG4gIC8vIFN0YXR1cyBhbmQgbWV0YWRhdGFcbiAgc3RhdHVzOiB0ZXh0KCdzdGF0dXMnLCB7IGVudW06IFsncGVuZGluZycsICdjb21wbGV0ZWQnLCAndmVyaWZpZWQnXSB9KS5ub3ROdWxsKCkuZGVmYXVsdCgncGVuZGluZycpLFxuICBlbnRlcmVkQnk6IHRleHQoJ2VudGVyZWRfYnknKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcbiAgdmVyaWZpZWRCeTogdGV4dCgndmVyaWZpZWRfYnknKS5yZWZlcmVuY2VzKCgpID0+IHVzZXJzLmlkKSxcbiAgY2VydGlmaWNhdGVHZW5lcmF0ZWQ6IGJvb2xlYW4oJ2NlcnRpZmljYXRlX2dlbmVyYXRlZCcpLmRlZmF1bHQoZmFsc2UpLFxuICBjZXJ0aWZpY2F0ZVNlcmlhbDogdGV4dCgnY2VydGlmaWNhdGVfc2VyaWFsJykudW5pcXVlKCksXG4gIGNlcnRpZmljYXRlVXJsOiB0ZXh0KCdjZXJ0aWZpY2F0ZV91cmwnKSxcbiAgYWlGZWVkYmFja0dlbmVyYXRlZDogYm9vbGVhbignYWlfZmVlZGJhY2tfZ2VuZXJhdGVkJykuZGVmYXVsdChmYWxzZSksXG4gIHRlc3REYXRlOiB0aW1lc3RhbXAoJ3Rlc3RfZGF0ZScsIHsgbW9kZTogJ2RhdGUnIH0pLFxuXG4gIGNyZWF0ZWRBdDogdGltZXN0YW1wKCdjcmVhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbiAgdXBkYXRlZEF0OiB0aW1lc3RhbXAoJ3VwZGF0ZWRfYXQnKS5kZWZhdWx0Tm93KCkubm90TnVsbCgpLFxufSk7XG5cbi8vIEFJIGZlZWRiYWNrIHRhYmxlXG5leHBvcnQgY29uc3QgYWlGZWVkYmFjayA9IHBnVGFibGUoJ2FpX2ZlZWRiYWNrJywge1xuICBpZDogdGV4dCgnaWQnKS5wcmltYXJ5S2V5KCkuJGRlZmF1bHRGbigoKSA9PiBjcmVhdGVJZCgpKSxcbiAgdGVzdFJlc3VsdElkOiB0ZXh0KCd0ZXN0X3Jlc3VsdF9pZCcpLm5vdE51bGwoKS5yZWZlcmVuY2VzKCgpID0+IHRlc3RSZXN1bHRzLmlkLCB7IG9uRGVsZXRlOiAnY2FzY2FkZScgfSksXG5cbiAgLy8gRmVlZGJhY2sgZm9yIGVhY2ggc2tpbGxcbiAgbGlzdGVuaW5nRmVlZGJhY2s6IHRleHQoJ2xpc3RlbmluZ19mZWVkYmFjaycpLFxuICByZWFkaW5nRmVlZGJhY2s6IHRleHQoJ3JlYWRpbmdfZmVlZGJhY2snKSxcbiAgd3JpdGluZ0ZlZWRiYWNrOiB0ZXh0KCd3cml0aW5nX2ZlZWRiYWNrJyksXG4gIHNwZWFraW5nRmVlZGJhY2s6IHRleHQoJ3NwZWFraW5nX2ZlZWRiYWNrJyksXG5cbiAgLy8gT3ZlcmFsbCBmZWVkYmFjayBhbmQgcmVjb21tZW5kYXRpb25zXG4gIG92ZXJhbGxGZWVkYmFjazogdGV4dCgnb3ZlcmFsbF9mZWVkYmFjaycpLFxuICBzdHVkeVJlY29tbWVuZGF0aW9uczogdGV4dCgnc3R1ZHlfcmVjb21tZW5kYXRpb25zJyksXG5cbiAgLy8gU3RyZW5ndGhzIGFuZCB3ZWFrbmVzc2VzXG4gIHN0cmVuZ3RoczoganNvbignc3RyZW5ndGhzJykuJHR5cGU8c3RyaW5nW10+KCksXG4gIHdlYWtuZXNzZXM6IGpzb24oJ3dlYWtuZXNzZXMnKS4kdHlwZTxzdHJpbmdbXT4oKSxcblxuICAvLyBTdHVkeSBwbGFuIHN1Z2dlc3Rpb25zXG4gIHN0dWR5UGxhbjoganNvbignc3R1ZHlfcGxhbicpLiR0eXBlPHtcbiAgICB0aW1lZnJhbWU6IHN0cmluZztcbiAgICBmb2N1c0FyZWFzOiBzdHJpbmdbXTtcbiAgICByZXNvdXJjZXM6IHN0cmluZ1tdO1xuICAgIHByYWN0aWNlQWN0aXZpdGllczogc3RyaW5nW107XG4gIH0+KCksXG5cbiAgZ2VuZXJhdGVkQXQ6IHRpbWVzdGFtcCgnZ2VuZXJhdGVkX2F0JykuZGVmYXVsdE5vdygpLm5vdE51bGwoKSxcbn0pO1xuXG4vLyBFeHBvcnQgdHlwZXNcbmV4cG9ydCB0eXBlIFVzZXIgPSB0eXBlb2YgdXNlcnMuJGluZmVyU2VsZWN0O1xuZXhwb3J0IHR5cGUgTmV3VXNlciA9IHR5cGVvZiB1c2Vycy4kaW5mZXJJbnNlcnQ7XG5leHBvcnQgdHlwZSBDYW5kaWRhdGUgPSB0eXBlb2YgY2FuZGlkYXRlcy4kaW5mZXJTZWxlY3Q7XG5leHBvcnQgdHlwZSBOZXdDYW5kaWRhdGUgPSB0eXBlb2YgY2FuZGlkYXRlcy4kaW5mZXJJbnNlcnQ7XG5leHBvcnQgdHlwZSBUZXN0UmVzdWx0ID0gdHlwZW9mIHRlc3RSZXN1bHRzLiRpbmZlclNlbGVjdDtcbmV4cG9ydCB0eXBlIE5ld1Rlc3RSZXN1bHQgPSB0eXBlb2YgdGVzdFJlc3VsdHMuJGluZmVySW5zZXJ0O1xuZXhwb3J0IHR5cGUgQUlGZWVkYmFjayA9IHR5cGVvZiBhaUZlZWRiYWNrLiRpbmZlclNlbGVjdDtcbmV4cG9ydCB0eXBlIE5ld0FJRmVlZGJhY2sgPSB0eXBlb2YgYWlGZWVkYmFjay4kaW5mZXJJbnNlcnQ7XG4iXSwibmFtZXMiOlsicGdUYWJsZSIsInRleHQiLCJ0aW1lc3RhbXAiLCJpbnRlZ2VyIiwiZGVjaW1hbCIsImJvb2xlYW4iLCJqc29uIiwiY3JlYXRlSWQiLCJ1c2VycyIsImlkIiwicHJpbWFyeUtleSIsIiRkZWZhdWx0Rm4iLCJuYW1lIiwiZW1haWwiLCJub3ROdWxsIiwidW5pcXVlIiwiZW1haWxWZXJpZmllZCIsIm1vZGUiLCJpbWFnZSIsInBhc3N3b3JkIiwicm9sZSIsImVudW0iLCJkZWZhdWx0IiwiY3JlYXRlZEF0IiwiZGVmYXVsdE5vdyIsInVwZGF0ZWRBdCIsImFjY291bnRzIiwidXNlcklkIiwicmVmZXJlbmNlcyIsIm9uRGVsZXRlIiwidHlwZSIsInByb3ZpZGVyIiwicHJvdmlkZXJBY2NvdW50SWQiLCJyZWZyZXNoX3Rva2VuIiwiYWNjZXNzX3Rva2VuIiwiZXhwaXJlc19hdCIsInRva2VuX3R5cGUiLCJzY29wZSIsImlkX3Rva2VuIiwic2Vzc2lvbl9zdGF0ZSIsInNlc3Npb25zIiwic2Vzc2lvblRva2VuIiwiZXhwaXJlcyIsInZlcmlmaWNhdGlvblRva2VucyIsImlkZW50aWZpZXIiLCJ0b2tlbiIsImNhbmRpZGF0ZXMiLCJmdWxsTmFtZSIsInBob25lTnVtYmVyIiwiZGF0ZU9mQmlydGgiLCJuYXRpb25hbGl0eSIsInBhc3Nwb3J0TnVtYmVyIiwidGVzdERhdGUiLCJ0ZXN0Q2VudGVyIiwicGhvdG9VcmwiLCJ0ZXN0UmVzdWx0cyIsImNhbmRpZGF0ZUlkIiwibGlzdGVuaW5nU2NvcmUiLCJwcmVjaXNpb24iLCJzY2FsZSIsImxpc3RlbmluZ0JhbmRTY29yZSIsInJlYWRpbmdTY29yZSIsInJlYWRpbmdCYW5kU2NvcmUiLCJ3cml0aW5nVGFzazFTY29yZSIsIndyaXRpbmdUYXNrMlNjb3JlIiwid3JpdGluZ0JhbmRTY29yZSIsInNwZWFraW5nRmx1ZW5jeVNjb3JlIiwic3BlYWtpbmdMZXhpY2FsU2NvcmUiLCJzcGVha2luZ0dyYW1tYXJTY29yZSIsInNwZWFraW5nUHJvbnVuY2lhdGlvblNjb3JlIiwic3BlYWtpbmdCYW5kU2NvcmUiLCJvdmVyYWxsQmFuZFNjb3JlIiwic3RhdHVzIiwiZW50ZXJlZEJ5IiwidmVyaWZpZWRCeSIsImNlcnRpZmljYXRlR2VuZXJhdGVkIiwiY2VydGlmaWNhdGVTZXJpYWwiLCJjZXJ0aWZpY2F0ZVVybCIsImFpRmVlZGJhY2tHZW5lcmF0ZWQiLCJhaUZlZWRiYWNrIiwidGVzdFJlc3VsdElkIiwibGlzdGVuaW5nRmVlZGJhY2siLCJyZWFkaW5nRmVlZGJhY2siLCJ3cml0aW5nRmVlZGJhY2siLCJzcGVha2luZ0ZlZWRiYWNrIiwib3ZlcmFsbEZlZWRiYWNrIiwic3R1ZHlSZWNvbW1lbmRhdGlvbnMiLCJzdHJlbmd0aHMiLCIkdHlwZSIsIndlYWtuZXNzZXMiLCJzdHVkeVBsYW4iLCJnZW5lcmF0ZWRBdCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fresults%2Froute&page=%2Fapi%2Fadmin%2Fresults%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fresults%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();