import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { db } from '@/lib/db';
import { candidates, testResults } from '@/lib/db/schema';
import { eq, gte, lte, and, count, avg, sql } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const session = await auth();

    if (!session || session.user?.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { from, to } = await request.json();
    const fromDate = new Date(from);
    const toDate = new Date(to);

    // Overview statistics
    const totalCandidatesResult = await db
      .select({ count: count() })
      .from(candidates)
      .where(and(
        gte(candidates.createdAt, fromDate),
        lte(candidates.createdAt, toDate)
      ));

    const totalResultsResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(and(
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    const averageBandScoreResult = await db
      .select({ avg: avg(testResults.overallBandScore) })
      .from(testResults)
      .where(and(
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    const certificatesGeneratedResult = await db
      .select({ count: count() })
      .from(testResults)
      .where(and(
        eq(testResults.certificateGenerated, true),
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ));

    // Band score distribution
    const bandScoreDistribution = await db
      .select({
        score: testResults.overallBandScore,
        count: count(),
      })
      .from(testResults)
      .where(and(
        gte(testResults.createdAt, fromDate),
        lte(testResults.createdAt, toDate)
      ))
      .groupBy(testResults.overallBandScore)
      .orderBy(testResults.overallBandScore);

    // Calculate percentages for band score distribution
    const totalForDistribution = bandScoreDistribution.reduce((sum, item) => sum + item.count, 0);
    const processedBandDistribution = bandScoreDistribution
      .filter(item => item.score !== null)
      .map(item => ({
        score: item.score!.toString(),
        count: item.count,
        percentage: totalForDistribution > 0 ? (item.count / totalForDistribution) * 100 : 0,
      }));

    // Test center statistics
    const testCenterStats = await db
      .select({
        center: candidates.testCenter,
        candidates: count(),
        avgScore: avg(testResults.overallBandScore),
      })
      .from(candidates)
      .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
      .where(and(
        gte(candidates.createdAt, fromDate),
        lte(candidates.createdAt, toDate)
      ))
      .groupBy(candidates.testCenter)
      .orderBy(candidates.testCenter);

    // Monthly statistics
    const monthlyStats = await db
      .select({
        month: sql<string>`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`,
        candidates: count(candidates.id),
        results: count(testResults.id),
        avgScore: avg(testResults.overallBandScore),
      })
      .from(candidates)
      .leftJoin(testResults, eq(candidates.id, testResults.candidateId))
      .where(and(
        gte(candidates.createdAt, fromDate),
        lte(candidates.createdAt, toDate)
      ))
      .groupBy(sql`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`)
      .orderBy(sql`TO_CHAR(${candidates.createdAt}, 'YYYY-MM')`);

    // Recent activity (last 7 days)
    const recentActivity = [];

    for (let i = 6; i >= 0; i--) {
      const date = new Date(Date.now() - i * 24 * 60 * 60 * 1000);
      const startOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const endOfDay = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

      const candidatesCount = await db
        .select({ count: count() })
        .from(candidates)
        .where(and(
          gte(candidates.createdAt, startOfDay),
          lte(candidates.createdAt, endOfDay)
        ));

      const resultsCount = await db
        .select({ count: count() })
        .from(testResults)
        .where(and(
          gte(testResults.createdAt, startOfDay),
          lte(testResults.createdAt, endOfDay)
        ));

      recentActivity.push({
        date: startOfDay.toISOString().split('T')[0],
        candidates: candidatesCount[0]?.count || 0,
        results: resultsCount[0]?.count || 0,
      });
    }

    const reportData = {
      overview: {
        totalCandidates: totalCandidatesResult[0]?.count || 0,
        totalResults: totalResultsResult[0]?.count || 0,
        averageBandScore: Number(averageBandScoreResult[0]?.avg) || 0,
        certificatesGenerated: certificatesGeneratedResult[0]?.count || 0,
      },
      monthlyStats: monthlyStats.map(stat => ({
        month: stat.month,
        candidates: stat.candidates,
        results: stat.results,
        avgScore: Number(stat.avgScore) || 0,
      })),
      bandScoreDistribution: processedBandDistribution,
      testCenterStats: testCenterStats.map(stat => ({
        center: stat.center,
        candidates: stat.candidates,
        avgScore: Number(stat.avgScore) || 0,
      })),
      recentActivity,
    };

    return NextResponse.json(reportData);

  } catch (error) {
    console.error('Reports error:', error);
    return NextResponse.json(
      { error: 'Failed to generate reports' },
      { status: 500 }
    );
  }
}
