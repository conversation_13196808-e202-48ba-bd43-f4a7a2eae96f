/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/route";
exports.ids = ["app/api/admin/candidates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/route.ts */ \"(rsc)/./src/app/api/admin/candidates/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/route\",\n        pathname: \"/api/admin/candidates\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/admin/candidates/route.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const search = searchParams.get('search') || '';\n        const includeResults = searchParams.get('includeResults') === 'true';\n        const offset = (page - 1) * limit;\n        // Build search conditions\n        const searchConditions = search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.candidateNumber, `%${search}%`)) : undefined;\n        if (includeResults) {\n            // Get candidates with their test results\n            const candidatesWithResults = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                id: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id,\n                candidateNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.candidateNumber,\n                fullName: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName,\n                email: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email,\n                phoneNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.phoneNumber,\n                dateOfBirth: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.dateOfBirth,\n                nationality: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.nationality,\n                passportNumber: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber,\n                testDate: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testDate,\n                testCenter: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.testCenter,\n                photoUrl: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.photoUrl,\n                createdAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt,\n                updatedAt: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.updatedAt,\n                resultId: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.id,\n                listeningBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.listeningBandScore,\n                readingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.readingBandScore,\n                writingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.writingBandScore,\n                speakingBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.speakingBandScore,\n                overallBandScore: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.overallBandScore,\n                status: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.status\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).leftJoin(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults, (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.eq)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id, _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.testResults.candidateId)).where(searchConditions).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt)).limit(limit).offset(offset);\n            // Transform the data to include hasResult flag and nested result object\n            const transformedCandidates = candidatesWithResults.map((candidate)=>({\n                    id: candidate.id,\n                    candidateNumber: candidate.candidateNumber,\n                    fullName: candidate.fullName,\n                    email: candidate.email,\n                    phoneNumber: candidate.phoneNumber,\n                    dateOfBirth: candidate.dateOfBirth,\n                    nationality: candidate.nationality,\n                    passportNumber: candidate.passportNumber,\n                    testDate: candidate.testDate,\n                    testCenter: candidate.testCenter,\n                    photoUrl: candidate.photoUrl,\n                    createdAt: candidate.createdAt,\n                    updatedAt: candidate.updatedAt,\n                    hasResult: !!candidate.resultId,\n                    result: candidate.resultId ? {\n                        id: candidate.resultId,\n                        listeningBandScore: candidate.listeningBandScore,\n                        readingBandScore: candidate.readingBandScore,\n                        writingBandScore: candidate.writingBandScore,\n                        speakingBandScore: candidate.speakingBandScore,\n                        overallBandScore: candidate.overallBandScore,\n                        status: candidate.status\n                    } : null\n                }));\n            // Get total count\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions);\n            const total = totalResult[0]?.count || 0;\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: transformedCandidates,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        } else {\n            // Get total count\n            const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.count)()\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions);\n            const total = totalResult[0]?.count || 0;\n            // Get candidates\n            const candidatesList = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt)).limit(limit).offset(offset);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                candidates: candidatesList,\n                total,\n                page,\n                limit,\n                totalPages: Math.ceil(total / limit)\n            });\n        }\n    } catch (error) {\n        console.error('Error fetching candidates:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'fullName',\n            'email',\n            'phoneNumber',\n            'dateOfBirth',\n            'nationality',\n            'passportNumber',\n            'testDate',\n            'testCenter'\n        ];\n        for (const field of requiredFields){\n            if (!data[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Generate candidate number if not provided\n        let candidateNumber = data.candidateNumber;\n        if (!candidateNumber) {\n            // Get the count of existing candidates to generate next number\n            const candidateCount = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n                count: _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.id\n            }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates);\n            const nextNumber = candidateCount.length + 1;\n            candidateNumber = nextNumber.toString().padStart(3, '0');\n        }\n        // Create candidate\n        const newCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).values({\n            candidateNumber,\n            fullName: data.fullName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            dateOfBirth: new Date(data.dateOfBirth),\n            nationality: data.nationality,\n            passportNumber: data.passportNumber,\n            testDate: new Date(data.testDate),\n            testCenter: data.testCenter,\n            photoUrl: data.photoUrl,\n            photoData: data.photoData\n        }).returning();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newCandidate[0], {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating candidate:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('unique')) {\n            if (error.message.includes('email')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Email address already exists'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('passport')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Passport number already exists'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_number').notNull().unique(),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    photoData: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_data'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();