/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/admin/candidates/route";
exports.ids = ["app/api/admin/candidates/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "postgres":
/*!***************************!*\
  !*** external "postgres" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = import("postgres");;

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/admin/candidates/route.ts */ \"(rsc)/./src/app/api/admin/candidates/route.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__]);\nC_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/admin/candidates/route\",\n        pathname: \"/api/admin/candidates\",\n        filename: \"route\",\n        bundlePath: \"app/api/admin/candidates/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\IELTS-Certification-System\\\\src\\\\app\\\\api\\\\admin\\\\candidates\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_Windows_11_Desktop_codes_IELTS_Certification_System_src_app_api_admin_candidates_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/admin/candidates/route.ts":
/*!***********************************************!*\
  !*** ./src/app/api/admin/candidates/route.ts ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _lib_db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/functions/aggregate.js\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/select.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__]);\n([_lib_auth__WEBPACK_IMPORTED_MODULE_1__, _lib_db__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nasync function GET(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const page = parseInt(searchParams.get('page') || '1');\n        const limit = parseInt(searchParams.get('limit') || '20');\n        const search = searchParams.get('search') || '';\n        const offset = (page - 1) * limit;\n        // Build search conditions\n        const searchConditions = search ? (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.or)((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.fullName, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.email, `%${search}%`), (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_4__.ilike)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.passportNumber, `%${search}%`)) : undefined;\n        // Get total count\n        const totalResult = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select({\n            count: (0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.count)()\n        }).from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions);\n        const total = totalResult[0]?.count || 0;\n        // Get candidates\n        const candidatesList = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).where(searchConditions).orderBy((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_6__.desc)(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates.createdAt)).limit(limit).offset(offset);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            candidates: candidatesList,\n            total,\n            page,\n            limit,\n            totalPages: Math.ceil(total / limit)\n        });\n    } catch (error) {\n        console.error('Error fetching candidates:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const session = await (0,_lib_auth__WEBPACK_IMPORTED_MODULE_1__.auth)();\n        if (!session || session.user?.role !== 'admin') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        const data = await request.json();\n        // Validate required fields\n        const requiredFields = [\n            'fullName',\n            'email',\n            'phoneNumber',\n            'dateOfBirth',\n            'nationality',\n            'passportNumber',\n            'testDate',\n            'testCenter'\n        ];\n        for (const field of requiredFields){\n            if (!data[field]) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: `${field} is required`\n                }, {\n                    status: 400\n                });\n            }\n        }\n        // Create candidate\n        const newCandidate = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.db.insert(_lib_db_schema__WEBPACK_IMPORTED_MODULE_3__.candidates).values({\n            fullName: data.fullName,\n            email: data.email,\n            phoneNumber: data.phoneNumber,\n            dateOfBirth: new Date(data.dateOfBirth),\n            nationality: data.nationality,\n            passportNumber: data.passportNumber,\n            testDate: new Date(data.testDate),\n            testCenter: data.testCenter,\n            photoUrl: data.photoUrl\n        }).returning();\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(newCandidate[0], {\n            status: 201\n        });\n    } catch (error) {\n        console.error('Error creating candidate:', error);\n        // Handle unique constraint violations\n        if (error instanceof Error && error.message.includes('unique')) {\n            if (error.message.includes('email')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Email address already exists'\n                }, {\n                    status: 409\n                });\n            }\n            if (error.message.includes('passport')) {\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: 'Passport number already exists'\n                }, {\n                    status: 409\n                });\n            }\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/admin/candidates/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   auth: () => (/* binding */ auth),\n/* harmony export */   handlers: () => (/* binding */ handlers),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/credentials */ \"(rsc)/./node_modules/next-auth/providers/credentials.js\");\n/* harmony import */ var _db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./db */ \"(rsc)/./src/lib/db/index.ts\");\n/* harmony import */ var _db_schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./db/schema */ \"(rsc)/./src/lib/db/schema.ts\");\n/* harmony import */ var drizzle_orm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm */ \"(rsc)/./node_modules/drizzle-orm/sql/expressions/conditions.js\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/bcryptjs/index.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_db__WEBPACK_IMPORTED_MODULE_2__]);\n_db__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n\n\n\nconst { handlers, auth, signIn, signOut } = (0,next_auth__WEBPACK_IMPORTED_MODULE_0__[\"default\"])({\n    session: {\n        strategy: 'jwt'\n    },\n    providers: [\n        (0,next_auth_providers_credentials__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            name: 'credentials',\n            credentials: {\n                email: {\n                    label: 'Email',\n                    type: 'email'\n                },\n                password: {\n                    label: 'Password',\n                    type: 'password'\n                }\n            },\n            async authorize (credentials) {\n                if (!credentials?.email || !credentials?.password) {\n                    return null;\n                }\n                try {\n                    // Find user in database\n                    const foundUser = await _db__WEBPACK_IMPORTED_MODULE_2__.db.select().from(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users).where((0,drizzle_orm__WEBPACK_IMPORTED_MODULE_5__.eq)(_db_schema__WEBPACK_IMPORTED_MODULE_3__.users.email, credentials.email)).limit(1);\n                    if (foundUser.length === 0) {\n                        return null;\n                    }\n                    const user = foundUser[0];\n                    // Check password\n                    if (!user.password) {\n                        return null;\n                    }\n                    const isValidPassword = await bcryptjs__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compare(credentials.password, user.password);\n                    if (!isValidPassword) {\n                        return null;\n                    }\n                    return {\n                        id: user.id,\n                        email: user.email,\n                        name: user.name,\n                        role: user.role\n                    };\n                } catch (error) {\n                    console.error('Authentication error:', error);\n                    return null;\n                }\n            }\n        })\n    ],\n    callbacks: {\n        async jwt ({ token, user }) {\n            if (user) {\n                token.id = user.id;\n                token.role = user.role;\n                token.email = user.email;\n                token.name = user.name;\n            }\n            return token;\n        },\n        async session ({ session, token }) {\n            if (token) {\n                session.user.id = token.id;\n                session.user.role = token.role;\n                session.user.email = token.email;\n                session.user.name = token.name;\n            }\n            return session;\n        }\n    },\n    pages: {\n        signIn: '/auth/signin'\n    }\n});\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/index.ts":
/*!*****************************!*\
  !*** ./src/lib/db/index.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.accounts),\n/* harmony export */   aiFeedback: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.aiFeedback),\n/* harmony export */   candidates: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.candidates),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   sessions: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.sessions),\n/* harmony export */   testResults: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.testResults),\n/* harmony export */   users: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.users),\n/* harmony export */   verificationTokens: () => (/* reexport safe */ _schema__WEBPACK_IMPORTED_MODULE_1__.verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/postgres-js */ \"(rsc)/./node_modules/drizzle-orm/postgres-js/driver.js\");\n/* harmony import */ var postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! postgres */ \"postgres\");\n/* harmony import */ var _schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./schema */ \"(rsc)/./src/lib/db/schema.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__]);\n([postgres__WEBPACK_IMPORTED_MODULE_0__, drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\nconst connectionString = process.env.DATABASE_URL;\n// Disable prefetch as it is not supported for \"Transaction\" pool mode\nconst client = (0,postgres__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(connectionString, {\n    prepare: false\n});\nconst db = (0,drizzle_orm_postgres_js__WEBPACK_IMPORTED_MODULE_2__.drizzle)(client, {\n    schema: _schema__WEBPACK_IMPORTED_MODULE_1__\n});\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiL2luZGV4LnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQWtEO0FBQ2xCO0FBQ0c7QUFFbkMsTUFBTUcsbUJBQW1CQyxRQUFRQyxHQUFHLENBQUNDLFlBQVk7QUFFakQsc0VBQXNFO0FBQ3RFLE1BQU1DLFNBQVNOLG9EQUFRQSxDQUFDRSxrQkFBa0I7SUFBRUssU0FBUztBQUFNO0FBQ3BELE1BQU1DLEtBQUtULGdFQUFPQSxDQUFDTyxRQUFRO0lBQUVMLE1BQU1BLHNDQUFBQTtBQUFDLEdBQUc7QUFFckIiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcV2luZG93cyAxMVxcRGVza3RvcFxcY29kZXNcXElFTFRTLUNlcnRpZmljYXRpb24tU3lzdGVtXFxzcmNcXGxpYlxcZGJcXGluZGV4LnRzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGRyaXp6bGUgfSBmcm9tICdkcml6emxlLW9ybS9wb3N0Z3Jlcy1qcyc7XG5pbXBvcnQgcG9zdGdyZXMgZnJvbSAncG9zdGdyZXMnO1xuaW1wb3J0ICogYXMgc2NoZW1hIGZyb20gJy4vc2NoZW1hJztcblxuY29uc3QgY29ubmVjdGlvblN0cmluZyA9IHByb2Nlc3MuZW52LkRBVEFCQVNFX1VSTCE7XG5cbi8vIERpc2FibGUgcHJlZmV0Y2ggYXMgaXQgaXMgbm90IHN1cHBvcnRlZCBmb3IgXCJUcmFuc2FjdGlvblwiIHBvb2wgbW9kZVxuY29uc3QgY2xpZW50ID0gcG9zdGdyZXMoY29ubmVjdGlvblN0cmluZywgeyBwcmVwYXJlOiBmYWxzZSB9KTtcbmV4cG9ydCBjb25zdCBkYiA9IGRyaXp6bGUoY2xpZW50LCB7IHNjaGVtYSB9KTtcblxuZXhwb3J0ICogZnJvbSAnLi9zY2hlbWEnO1xuIl0sIm5hbWVzIjpbImRyaXp6bGUiLCJwb3N0Z3JlcyIsInNjaGVtYSIsImNvbm5lY3Rpb25TdHJpbmciLCJwcm9jZXNzIiwiZW52IiwiREFUQUJBU0VfVVJMIiwiY2xpZW50IiwicHJlcGFyZSIsImRiIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/index.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db/schema.ts":
/*!******************************!*\
  !*** ./src/lib/db/schema.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   accounts: () => (/* binding */ accounts),\n/* harmony export */   aiFeedback: () => (/* binding */ aiFeedback),\n/* harmony export */   candidates: () => (/* binding */ candidates),\n/* harmony export */   sessions: () => (/* binding */ sessions),\n/* harmony export */   testResults: () => (/* binding */ testResults),\n/* harmony export */   users: () => (/* binding */ users),\n/* harmony export */   verificationTokens: () => (/* binding */ verificationTokens)\n/* harmony export */ });\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/table.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/text.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/timestamp.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/integer.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/numeric.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/boolean.js\");\n/* harmony import */ var drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! drizzle-orm/pg-core */ \"(rsc)/./node_modules/drizzle-orm/pg-core/columns/json.js\");\n/* harmony import */ var _paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @paralleldrive/cuid2 */ \"(rsc)/./node_modules/@paralleldrive/cuid2/index.js\");\n\n\n// Users table for authentication\nconst users = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('users', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    name: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('name'),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    emailVerified: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('emailVerified', {\n        mode: 'date'\n    }),\n    image: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('image'),\n    password: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('password'),\n    role: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('role', {\n        enum: [\n            'admin',\n            'test_checker'\n        ]\n    }).notNull().default('test_checker'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Accounts table for OAuth\nconst accounts = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('accounts', {\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('type').notNull(),\n    provider: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('provider').notNull(),\n    providerAccountId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('providerAccountId').notNull(),\n    refresh_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('refresh_token'),\n    access_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('access_token'),\n    expires_at: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_4__.integer)('expires_at'),\n    token_type: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token_type'),\n    scope: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('scope'),\n    id_token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id_token'),\n    session_state: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('session_state')\n});\n// Sessions table for authentication\nconst sessions = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('sessions', {\n    sessionToken: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('sessionToken').primaryKey(),\n    userId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('userId').notNull().references(()=>users.id, {\n        onDelete: 'cascade'\n    }),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Verification tokens\nconst verificationTokens = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('verificationTokens', {\n    identifier: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('identifier').notNull(),\n    token: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('token').notNull(),\n    expires: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('expires', {\n        mode: 'date'\n    }).notNull()\n});\n// Candidates table\nconst candidates = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('candidates', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    fullName: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('full_name').notNull(),\n    email: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('email').notNull().unique(),\n    phoneNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('phone_number').notNull(),\n    dateOfBirth: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('date_of_birth', {\n        mode: 'date'\n    }).notNull(),\n    nationality: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('nationality').notNull(),\n    passportNumber: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('passport_number').notNull().unique(),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }).notNull(),\n    testCenter: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_center').notNull(),\n    photoUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('photo_url'),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// Test results table\nconst testResults = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('test_results', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    candidateId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('candidate_id').notNull().references(()=>candidates.id, {\n        onDelete: 'cascade'\n    }),\n    // Listening scores\n    listeningScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_score', {\n        precision: 3,\n        scale: 1\n    }),\n    listeningBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('listening_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Reading scores\n    readingScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_score', {\n        precision: 3,\n        scale: 1\n    }),\n    readingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('reading_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Writing scores\n    writingTask1Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task1_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingTask2Score: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_task2_score', {\n        precision: 2,\n        scale: 1\n    }),\n    writingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('writing_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Speaking scores\n    speakingFluencyScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_fluency_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingLexicalScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_lexical_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingGrammarScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_grammar_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingPronunciationScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_pronunciation_score', {\n        precision: 2,\n        scale: 1\n    }),\n    speakingBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('speaking_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Overall score\n    overallBandScore: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_5__.decimal)('overall_band_score', {\n        precision: 2,\n        scale: 1\n    }),\n    // Status and metadata\n    status: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('status', {\n        enum: [\n            'pending',\n            'completed',\n            'verified'\n        ]\n    }).notNull().default('pending'),\n    enteredBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('entered_by').references(()=>users.id),\n    verifiedBy: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('verified_by').references(()=>users.id),\n    certificateGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('certificate_generated').default(false),\n    certificateSerial: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_serial').unique(),\n    certificateUrl: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('certificate_url'),\n    aiFeedbackGenerated: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_6__.boolean)('ai_feedback_generated').default(false),\n    testDate: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('test_date', {\n        mode: 'date'\n    }),\n    createdAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('created_at').defaultNow().notNull(),\n    updatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('updated_at').defaultNow().notNull()\n});\n// AI feedback table\nconst aiFeedback = (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_1__.pgTable)('ai_feedback', {\n    id: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('id').primaryKey().$defaultFn(()=>(0,_paralleldrive_cuid2__WEBPACK_IMPORTED_MODULE_0__.createId)()),\n    testResultId: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('test_result_id').notNull().references(()=>testResults.id, {\n        onDelete: 'cascade'\n    }),\n    // Feedback for each skill\n    listeningFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('listening_feedback'),\n    readingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('reading_feedback'),\n    writingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('writing_feedback'),\n    speakingFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('speaking_feedback'),\n    // Overall feedback and recommendations\n    overallFeedback: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('overall_feedback'),\n    studyRecommendations: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_2__.text)('study_recommendations'),\n    // Strengths and weaknesses\n    strengths: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('strengths').$type(),\n    weaknesses: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('weaknesses').$type(),\n    // Study plan suggestions\n    studyPlan: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_7__.json)('study_plan').$type(),\n    generatedAt: (0,drizzle_orm_pg_core__WEBPACK_IMPORTED_MODULE_3__.timestamp)('generated_at').defaultNow().notNull()\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db/schema.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@auth","vendor-chunks/next-auth","vendor-chunks/drizzle-orm","vendor-chunks/oauth4webapi","vendor-chunks/jose","vendor-chunks/bcryptjs","vendor-chunks/@noble","vendor-chunks/preact","vendor-chunks/preact-render-to-string","vendor-chunks/@paralleldrive","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fadmin%2Fcandidates%2Froute&page=%2Fapi%2Fadmin%2Fcandidates%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fadmin%2Fcandidates%2Froute.ts&appDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CWindows%2011%5CDesktop%5Ccodes%5CIELTS-Certification-System&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();